"use client"

import React, { useEffect, useRef, useState, useCallback } from 'react'
import { useTheme } from 'next-themes'
import { Button } from '@/components/ui/button'
import { X, Maximize2, Minimize2 } from 'lucide-react'
import { cn } from '@/lib/utils'

// ✅ Fix SSR: Dynamic imports for browser-only xterm.js
let Terminal: any = null
let FitAddon: any = null
let WebLinksAddon: any = null

// ✅ Fix SSR: Only import xterm.js on client side
if (typeof window !== 'undefined') {
  import('xterm').then(module => {
    Terminal = module.Terminal
  })
  import('xterm-addon-fit').then(module => {
    FitAddon = module.FitAddon
  })
  import('xterm-addon-web-links').then(module => {
    WebLinksAddon = module.WebLinksAddon
  })
  // ✅ Task 83: Import xterm.js styles only on client
  import('xterm/css/xterm.css')
}

interface TerminalPanelProps {
  sessionId?: string
  shellType?: 'bash' | 'powershell' | 'cmd' | 'zsh'
  onClose?: () => void
  className?: string
  isFullscreen?: boolean
  onToggleFullscreen?: () => void
}

export default function TerminalPanel({
  sessionId,
  shellType = 'bash',
  onClose,
  className,
  isFullscreen = false,
  onToggleFullscreen
}: TerminalPanelProps) {
  const terminalRef = useRef<HTMLDivElement>(null)
  const terminalInstanceRef = useRef<any | null>(null)
  const fitAddonRef = useRef<any | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [backendSessionId, setBackendSessionId] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const { theme } = useTheme()
  const isDark = theme === 'dark'

  // ✅ Task 83: Terminal API access
  const terminalAPI = typeof window !== 'undefined' && (window as any).electronAPI?.terminalAPI

  // ✅ Task 83: Cleanup function for event listeners
  const cleanupRef = useRef<(() => void)[]>([])

  // ✅ Task 83: Initialize terminal instance with SSR fix
  const initializeTerminal = useCallback(async () => {
    if (!terminalRef.current || !terminalAPI) {
      console.warn('⚠️ Terminal ref or API not available')
      return
    }

    // ✅ Fix SSR: Wait for dynamic imports to complete
    if (!Terminal || !FitAddon || !WebLinksAddon) {
      console.warn('⚠️ xterm.js modules not loaded yet, retrying...')
      setTimeout(initializeTerminal, 100)
      return
    }

    setIsLoading(false)

    try {
      // Create xterm.js terminal instance with production-ready configuration
      const terminal = new Terminal({
        theme: {
          background: isDark ? '#1e1e1e' : '#ffffff',
          foreground: isDark ? '#ffffff' : '#000000',
          cursor: isDark ? '#ffffff' : '#000000',
          selection: isDark ? '#ffffff40' : '#00000040',
          black: isDark ? '#000000' : '#000000',
          red: isDark ? '#ff6b6b' : '#d63031',
          green: isDark ? '#51cf66' : '#00b894',
          yellow: isDark ? '#ffd43b' : '#fdcb6e',
          blue: isDark ? '#74c0fc' : '#0984e3',
          magenta: isDark ? '#f06292' : '#e84393',
          cyan: isDark ? '#4dd0e1' : '#00cec9',
          white: isDark ? '#ffffff' : '#636e72'
        },
        fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, "Courier New", monospace',
        fontSize: 14,
        lineHeight: 1.2,
        cursorBlink: true,
        cursorStyle: 'block',
        scrollback: 1000,
        tabStopWidth: 4,
        convertEol: true, // ✅ Task 83: Required for proper line ending handling
        allowTransparency: false,
        bellStyle: 'none'
      })

      // ✅ Task 83: Add required addons
      const fitAddon = new FitAddon()
      const webLinksAddon = new WebLinksAddon()

      terminal.loadAddon(fitAddon)
      terminal.loadAddon(webLinksAddon)

      // Store references
      terminalInstanceRef.current = terminal
      fitAddonRef.current = fitAddon

      // ✅ Task 83: Open terminal in DOM element
      terminal.open(terminalRef.current)
      fitAddon.fit()

      // ✅ Task 83: Start backend terminal session
      const result = await terminalAPI.startTerminal(terminal.cols, terminal.rows, sessionId)

      if (!result.success) {
        console.error('❌ Failed to start terminal session:', result.error)
        terminal.write('\r\n❌ Failed to connect to terminal backend\r\n')
        return
      }

      const backendId = result.sessionId
      setBackendSessionId(backendId)
      setIsConnected(true)

      console.log(`✅ Terminal session started: ${backendId}`)

      // ✅ Task 83: Set up bidirectional communication

      // Handle data from backend PTY
      const dataCleanup = terminalAPI.onTerminalData((receivedSessionId: string, data: string) => {
        if (receivedSessionId === backendId && terminal) {
          terminal.write(data)
        }
      })

      // Handle terminal exit
      const exitCleanup = terminalAPI.onTerminalExit((receivedSessionId: string, exitCode: number) => {
        if (receivedSessionId === backendId) {
          console.log(`Terminal session ${backendId} exited with code ${exitCode}`)
          setIsConnected(false)
          terminal.write(`\r\n[Process exited with code ${exitCode}]\r\n`)
        }
      })

      // ✅ Task 83: Handle user input to backend
      const inputDisposable = terminal.onData((data: string) => {
        if (backendId && isConnected) {
          terminalAPI.writeToTerminal(backendId, data)
        }
      })

      // Store cleanup functions
      cleanupRef.current = [
        dataCleanup,
        exitCleanup,
        () => inputDisposable.dispose()
      ]

      // Focus terminal
      terminal.focus()

    } catch (error) {
      console.error('❌ Error initializing terminal:', error)
    }
  }, [terminalAPI, sessionId, isDark, isConnected])

  // ✅ Task 83: Handle window resize
  const handleResize = useCallback(() => {
    if (fitAddonRef.current && terminalInstanceRef.current && backendSessionId && terminalAPI) {
      fitAddonRef.current.fit()
      const { cols, rows } = terminalInstanceRef.current
      terminalAPI.resizeTerminal(backendSessionId, cols, rows)
    }
  }, [backendSessionId, terminalAPI])

  // ✅ Task 83: Initialize terminal on mount
  useEffect(() => {
    if (terminalAPI) {
      initializeTerminal()
    } else {
      console.warn('⚠️ Terminal API not available - cannot initialize terminal')
    }

    return () => {
      // Cleanup on unmount
      cleanupRef.current.forEach(cleanup => cleanup())
      cleanupRef.current = []

      if (backendSessionId && terminalAPI) {
        terminalAPI.closeTerminal(backendSessionId)
      }

      if (terminalInstanceRef.current) {
        terminalInstanceRef.current.dispose()
        terminalInstanceRef.current = null
      }
    }
  }, [initializeTerminal, terminalAPI, backendSessionId])

  // ✅ Task 83: Handle resize events
  useEffect(() => {
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [handleResize])

  // ✅ Task 83: Handle theme changes
  useEffect(() => {
    if (terminalInstanceRef.current) {
      terminalInstanceRef.current.options.theme = {
        background: isDark ? '#1e1e1e' : '#ffffff',
        foreground: isDark ? '#ffffff' : '#000000',
        cursor: isDark ? '#ffffff' : '#000000',
        selection: isDark ? '#ffffff40' : '#00000040'
      }
    }
  }, [isDark])

  return (
    <div className={cn('flex flex-col h-full bg-editor-terminal-bg', className)}>
      {/* ✅ Task 83: Terminal header with controls */}
      <div className="flex items-center justify-between h-8 px-2 border-b border-editor-border bg-editor-sidebar-bg">
        <div className="flex items-center space-x-2">
          <span className="text-xs text-muted-foreground">
            {shellType} {backendSessionId && `(${backendSessionId.slice(-8)})`}
          </span>
          <div className={cn(
            'w-2 h-2 rounded-full',
            isConnected ? 'bg-green-500' : 'bg-red-500'
          )} />
        </div>

        <div className="flex items-center space-x-1">
          {onToggleFullscreen && (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={onToggleFullscreen}
              title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
            >
              {isFullscreen ? <Minimize2 className="h-3 w-3" /> : <Maximize2 className="h-3 w-3" />}
            </Button>
          )}

          {onClose && (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={onClose}
              title="Close terminal"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>

      {/* ✅ Task 83: Real xterm.js terminal container */}
      <div className="flex-1 relative overflow-hidden">
        {!terminalAPI ? (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="text-center">
              <div className="text-sm mb-1">Terminal API not available</div>
              <div className="text-xs text-muted-foreground/70">
                Running in web mode or Electron API not loaded
              </div>
            </div>
          </div>
        ) : isLoading ? (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="text-center">
              <div className="text-sm mb-1">Loading terminal...</div>
              <div className="text-xs text-muted-foreground/70">
                Initializing xterm.js components
              </div>
            </div>
          </div>
        ) : (
          <div
            ref={terminalRef}
            className="absolute inset-0 p-2"
            style={{
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", Consolas, "Courier New", monospace'
            }}
          />
        )}
      </div>
    </div>
  )
}
